import requests
from bs4 import BeautifulSoup
# import cloudscraper
import urllib.parse
from selenium import webdriver
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.keys import Keys
import json
import threading
import os
import time


domain = "https://comick.io/";
# create a cloudscraper instance
# scraper = cloudscraper.create_scraper(
#     browser={
#         "browser": "chrome",
#         "platform": "windows",
#         "desktop": True
#     },
# )

DIR = os.getcwd();



def download_chapter(pages: list[str], chapter_no: int) -> None:
    try:
        # download_images(chapter_imgs)
        
        print(f"\n📌 Downloading chapter {chapter_no} pages...")
        # download_images(pages)
        # print(f"✅ Done: Downloaded {len(pages)} images.")

        path = os.path.join(DIR, "chapters", str(f"chapter_{chapter_no}"))
        if not os.path.exists(path):
            os.makedirs(path)
        os.chdir(path)
        download_images(pages)
        os.chdir(DIR)
        print(f"✅ Done: Downloaded chapter {chapter_no}.")
    except Exception as e:
        print(f"Failed to download chapter {chapter_no}: {e}")

def download_images(chapter_imgs: list[str]) -> None:
    threads = []
    for i in range(len(chapter_imgs)):
        img = chapter_imgs[i]
        ext = img.split(".")[-1]
        t = threading.Thread(target=download_image, args=(f"{i}.{ext}", img))
        threads.append(t)
        t.start()

    for t in threads:
        t.join()

def download_image(name: str, url: str) -> bytes:
    domain = urllib.parse.urlparse(url).netloc
    HEADERS = {
        'Accept': 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
        'Accept': 'text/html',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'Host': domain, 'Accept-Language': 'en-ca', 'Referer': f'https://{domain}/',
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Connection': 'keep-alive',
        "Priority": 'i',
        "Sec-Ch-Ua": 'Brave";v="135", "Not-A.Brand";v="8", "Chromium";v="135',
        'Sec-Fetch-Storage-Access': 'active'
    }

    r = requests.get(url, headers=HEADERS, stream=True)
    if r.status_code != 200:
        print(f"Failed to download image -> {url}: {r.status_code}")
        return False
    
    with open(name, 'wb') as f:
        f.write(r.content)

    return True


def get_chapter_page(url: str, chapter_no: int): 
    driver = webdriver.Chrome()
    try:
        print(f"\nScraping chapter {chapter_no} pages")
        chapter_pages = []

        driver.get(url)

        page_source = driver.execute_script("return document.documentElement.outerHTML;")
        soup = BeautifulSoup(page_source, "html.parser")

        reader_container = soup.find('div', {'id': 'images-reader-container'})
        
        for page in reader_container.find_all('div', {'class': 'overflow-scroll remove-scroll-bar'}):
            img = page.find('img')
            chapter_pages.append(img['src'])

        
        return chapter_pages
    except Exception as e:
        driver.quit();
        print(f"Failed to get chapter page {chapter_no} -> {url}: {e}")
        return []


def get_chapter_info(soup: BeautifulSoup) -> dict[str, str]:
    anchor = soup.find('a')
    link = anchor.get('href')
    title = anchor.find('span').get('title')

    return {
        "no": title.split(" ")[1],
        "url": domain[0:-1] + link,
        "title": title,
        "release_date": ""
    }

def get_chapters(soup: BeautifulSoup) -> list[dict[str, str]]:
    items = []
    print(f"\nCrawling chapters...")
    chapter_info = soup.find('div', {'class': 'grid grid-cols-2 md:grid-cols-3 max-xl:px-1'}).find_next_sibling('div')
    chapters = chapter_info.find('table').find_all('td', {'class': 'customclass1'})

    temp_dict_check = {'1': '1', '5': '5', '6': '6', '7': '7', '8': '8', '9': '9', '10': '10',  '11': '11', '12': '12', '13': '13', '14': '14', '15': '15', '16': '16', '17': '17', '18': '18', '19': '19', '20': '20', '21': '21', '22': '22', '23': '23', '24': '24', '25': '25', '26': '26', '27': '27', '28': '28', '29': '29', '30': '30', '31': '31', '32': '32', '33': '33', '34': '34', '35': '35', '36': '36', '37': '37', '38': '38', '39': '39', '40': '40', '41': '41', '42': '42', '43': '43', '44': '44', '45': '45', '46': '46', '47': '47', '48': '48', '49': '49',}
    for chapter in chapters:
        info = get_chapter_info(chapter)
        if temp_dict_check.get(info.get('no')) is not None:
            continue

        items.append(info)
        temp_dict_check[info.get('no')] = info.get('no')
    
    del temp_dict_check
    print(f"✅ Done: Scraped {len(items)} chapters.\n")
    return items

def get_info(soup: BeautifulSoup) -> dict[str, str]:

    print("\nCrawling info...")

    container = soup.find("div", {"class": "grid grid-cols-2 md:grid-cols-3 max-xl:px-1"})
    poster = container.find("div", {"class": "mr-4 relative row-span-5"}).find("img")["src"]

    info = container.find("div", {"class": "md:col-span-2 text-sm md:text-base"})

    title = info.find("div", {'class': "justify-between break-words hidden md:block"}).find('h1').text
    short_title = info.find("div", {'class': "text-gray-500 dark:text-gray-400 overflow-auto md:mt-3"}).text

    print(f"✅ Done: Scraped info.")

    return {
        "poster": poster,
        # "status": status,
        "title": title,
        # "description": desc,
        "short_title": short_title
    }


def get_page(url: str):
    driver = webdriver.Chrome()
    driver.get(url)

    page_source = driver.execute_script("return document.documentElement.outerHTML;")
    soup = BeautifulSoup(page_source, "html.parser")

    # time.sleep(5)

    info = get_info(soup);
    chapters = get_chapters(soup)

    map = {"info": info, "chapters": chapters}

    driver.quit()

    return map


def main() -> None:
    # url = "https://comick.io/comic/martial-evolution-start-by-awakening-the-king-of-monsters/dsumiYCz-chapter-1-en"
    url = "https://comick.io/comic/martial-evolution-start-by-awakening-the-king-of-monsters?group=0"

    # r = requests.get(url)

    # if r.status_code != 200:
    #     print(f"Failed to get page -> {url}: {r.status_code}")
    #     return ""


    page = get_page(url)

    chapters = page.get('chapters')
    for chapter in chapters:
        no = 
        download_chapter(get_chapter_page(chapter.get('url')), chapter.get('no'))
        time.sleep(2)
    
    # download_chapter(get_chapter_page("https://comick.io/comic/martial-evolution-start-by-awakening-the-king-of-monsters/eccmCrqo-chapter-23-en"), 23)

    # json_str = json.dumps(page, indent=4)

    # print(f"json_str: {json_str}")

    
    # driver = webdriver.Chrome()
    # driver.get(url)

    # page_source = driver.execute_script("return document.documentElement.outerHTML;")

    # soup = BeautifulSoup(page_source, "html.parser")
    # reader_container = soup.find('div', {'id': 'images-reader-container'})

    # chapter_pages = []
    # for page in reader_container.find_all('div', {'class': 'overflow-scroll remove-scroll-bar'}):
    #     img = page.find('img')
    #     chapter_pages.append(img['src'])

    # download_chapter(chapter_pages, 1)
    # print(chapter_pages)




    # name = "chapter-1.html"

    # path = os.path.join(DIR, "chapters", str(f"chapter_{1}"))
    # if not os.path.exists(path):
    #     os.makedirs(path)
    # os.chdir(path)

    # with open(name, 'w', encoding="utf-8") as f:
    #     f.write(page_source)

    # os.chdir(DIR)

    # driver.quit()


    

    # print(page_source)

if __name__ == "__main__":
    main()
