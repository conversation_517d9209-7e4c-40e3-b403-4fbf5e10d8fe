import requests
from bs4 import BeautifulSoup
# import cloudscraper
import urllib.parse
from selenium import webdriver


domain = "https://mangafire.to"
# create a cloudscraper instance
# scraper = cloudscraper.create_scraper(
#     browser={
#         "browser": "chrome",
#         "platform": "windows",
#         "desktop": True
#     },
# )




def get_chapter_page(url: str): 

    domain = urllib.parse.urlparse(url).netloc
    HEADERS = {
        # 'Accept': 'image/png, image/svg+xml, image/*;q=0.8, video/*;q=0.8,*/*;q=0.5',
        'Accept': 'text/html',
        # 'Accept-Encoding': 'gzip, deflate, br',
        'Host': domain, 'Accept-Language': 'en-ca', 'Referer': f'https://{domain}',
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_6) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0.3 Safari/605.1.15',
        'Connection': 'keep-alive'
    }

    # print(f'Headers: f{HEADERS}\n\n')

    response = requests.get(url, headers=HEADERS)
    if response.status_code != 200:
        print(f"Failed to get chapter page -> {url}: {response.status_code}")
        return ""
    
    soup = BeautifulSoup(response.content, "html.parser")
    # images = soup.find("div", {"class": "page"}).find_all("img")
    div = soup.find('div')


    print(f"\n\n{div}\n\n")

    # return [image["src"] for image in images]

    return soup.text


def get_chapter_info(el: BeautifulSoup) -> dict[str, str]:
    no = el.attrs['data-number']
    url = domain + el.find("a")["href"]
    span = el.find("span")
    title =  span.text
    release_date = span.find_next_sibling("span").text

    return {
        "no": no,
        "url": url,
        "title": title,
        "release_date": release_date
    }

def get_chapters(soup: BeautifulSoup) -> list[dict[str, str]]:
    items = []
    chapters = soup.find("div", {"class": "list-body"}).find_all("li")

    for chapter in chapters:
        items.append(get_chapter_info(chapter))
    
    return items


def get_info(soup: BeautifulSoup) -> dict[str, str]:
    
    poster = soup.find("div", {"class": "poster"}).find("img")["src"]
    info = soup.find("div", {"class": "info"})
    desc = soup.find("div", {"class": "description"}).text
    status = info.find("p").text
    title = info.find("h6").text
    short_title = info.find('h1', attrs={"itemprop": "name"}).text

    return {
        "poster": poster,
        "status": status,
        "title": title,
        "description": desc,
        "short_title": short_title
    }

def get_page(url: str) -> dict[str, dict[str, str] | list[dict[str, str]]]:
    response = requests.get(url)
    if response.status_code != 200:
        print(f"Failed to get page -> {url}: {response.status_code}")
        return ""
    
    soup = BeautifulSoup(response.text, "html.parser")
    info = get_info(soup);
    chapters = get_chapters(soup)

    return {"info": info, "chapters": chapters}