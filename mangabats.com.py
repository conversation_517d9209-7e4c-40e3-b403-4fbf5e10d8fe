import requests
from bs4 import BeautifulSoup
# import cloudscraper
import urllib.parse
from selenium import webdriver
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.keys import Keys
import json
import threading
import os
import time


# create a cloudscraper instance
# scraper = cloudscraper.create_scraper(
#     browser={
#         "browser": "chrome",
#         "platform": "windows",
#         "desktop": True
#     },
# )

DIR = os.getcwd();



def download_chapter(chapter: dict[str, str]) -> None:
    print(f"\nCrawling chapter {chapter.get('no')}...")
    pages = get_chapter_page(chapter.get('url'))
    # download_images(chapter_imgs)
    print(f"📌 Scraped {len(pages)} pages.")
    print(f"📌 Downloading chapter pages...")
    # download_images(pages)
    # print(f"✅ Done: Downloaded {len(pages)} images.")

    path = os.path.join(DIR, "chapters", str(f"chapter_{chapter.get('no')}"))
    if not os.path.exists(path):
        os.makedirs(path)
    os.chdir(path)
    download_images(pages)
    os.chdir(DIR)
    print(f"✅ Done: Downloaded chapter {chapter.get('no')}.")

def download_images(chapter_imgs: list[dict[str, str]]) -> None:
    threads = []
    for i in range(len(chapter_imgs)):
        img = chapter_imgs[i]
        ext = img.get('link').split(".")[-1]
        t = threading.Thread(target=download_image, args=(f"{i}.{ext}", img.get('link')))
        threads.append(t)
        t.start()

    for t in threads:
        t.join()

def download_image(name: str, url: str) -> bytes:
    domain = urllib.parse.urlparse(url).netloc
    HEADERS = {
        'Accept': 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
        'Accept': 'text/html',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'Host': domain, 'Accept-Language': 'en-ca', 'Referer': f'https://{domain}/',
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Connection': 'keep-alive',
        "Priority": 'i',
        "Sec-Ch-Ua": 'Brave";v="135", "Not-A.Brand";v="8", "Chromium";v="135',
        'Sec-Fetch-Storage-Access': 'active'
    }

    r = requests.get(url, headers=HEADERS, stream=True)
    if r.status_code != 200:
        print(f"Failed to download image -> {url}: {r.status_code}")
        return False
    
    with open(name, 'wb') as f:
        f.write(r.content)

    return True


def get_chapter_page(url: str): 
    response = requests.get(url)
    if response.status_code != 200:
        print(f"Failed to get chapter page -> {url}: {response.status_code}")
        return ""
    
    chapter_imgs = []
    soup = BeautifulSoup(response.content, "html.parser")
    images = soup.find("div", {"class": "container-chapter-reader"}).find_all("img")

    for image in images:
        link = image["src"]
        title = image["alt"]
        chapter_imgs.append({
            "link": link,
            "title": title
        })

    return chapter_imgs


def get_chapter_info(soup: BeautifulSoup) -> dict[str, str]:
    anchor = soup.find('span').find('a')
    url = anchor["href"]
    title = anchor.text
    no = title.split(" ")[1]
    release_date = soup.find('span').find_next_siblings("span")[1]['title']


    return {
        "no": no,
        "url": url,
        "title": title,
        "release_date": release_date
    }

def get_chapters(soup: BeautifulSoup) -> list[dict[str, str]]:
    items = []
    chapters = soup.find("div", {"class": "chapter-list"}).find_all("div", {'class': 'row'})
    print("\nCrawling chapters...")

    for chapter in chapters:
        chapter_info = get_chapter_info(chapter)
        # chapter_imgs = get_chapter_page(chapter_info.get('url'))
        items.append(chapter_info)
    
    print(f"✅ Done: Scraped {len(items)} chapters.")
    return items


def get_info(soup: BeautifulSoup) -> dict[str, str]:

    print("\nCrawling info...")

    
    info = soup.find("ul", {"class": "manga-info-text"})
    poster = soup.find("div", {"class": "manga-info-pic"}).find("img")["src"]

    title_el = info.find("li")
    title = title_el.find("h2", {'class': "story-alternative"}).text.split(":")[1:]
    title = "".join(title).strip()
    short_title = title_el.find('h1').text

    status = title_el.find_next_sibling("li").find_next_sibling('li').text.split(":")[1].strip()
    desc = soup.find("div", {"id": "contentBox"}).text

    print(f"✅ Done: Scraped info.")

    return {
        "poster": poster,
        "status": status,
        "title": title,
        "description": desc,
        "short_title": short_title
    }

def get_page(url: str) -> dict[str, dict[str, str] | list[dict[str, str]]]:
    response = requests.get(url)
    if response.status_code != 200:
        print(f"Failed to get page -> {url}: {response.status_code}")
        return ""
    
    soup = BeautifulSoup(response.text, "html.parser")
    info = get_info(soup);
    chapters = get_chapters(soup)

    return {"info": info, "chapters": chapters}


def main() -> None:
    # url = "https://mangadex.org/manga/32a0a420-f48a-46ea-a8d0-46d3d26dbe75"
    # url = "https://comick.io/comic/supreme-curse-master"
    # url = "https://mangafire.to/manga/one-piecee.dkw"
    # url = "https://mangafire.to/manga/supreme-curse-master.mjqwp"
    url = "https://www.mangabats.com/manga/logging-10000-years-into-the-future"
    page = get_page(url)

    json_str = json.dumps(page, indent=4)

    # chapter_url = page.get('chapters')[0].get('url')
    # chapter_imgs = get_chapter_page(chapter_url)

    # download_images(chapter_imgs)
    # download_chapter(page.get('chapters')[0])

    print(json_str)

if __name__ == "__main__":
    main()
